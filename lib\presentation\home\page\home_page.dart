import 'package:flutter/material.dart';
import 'package:flutter_3d_controller/flutter_3d_controller.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  Flutter3DController controller = Flutter3DController();
  double rotationX = 0;
  double rotationY = 0;
  double rotationZ = 0;

  @override
  void initState() {
    super.initState();
  }

  void rotateModel() {
    controller.setCameraOrbit(rotationX, rotationY, rotationZ);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('3D Model Viewer'),
      ),
      body: Column(
        children: [
          Expanded(
            child: Flutter3DViewer(
              src: 'assets/model/untitled.glb',
              controller: controller,
              progressBarColor: Colors.blue,
              enableTouch: true, // Enable touch to manually rotate
              onProgress: (double progressValue) {
                debugPrint('model loading progress : $progressValue');
              },
              onLoad: (String modelAddress) {
                controller.setCameraTarget(0, 0, 0);
                debugPrint('model loaded : $modelAddress');
              },
              onError: (String error) {
                debugPrint('model failed to load : $error');
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Text('X Rotation: ${rotationX.toStringAsFixed(1)}'),
                Slider(
                  value: rotationX,
                  min: 0,
                  max: 360,
                  onChanged: (value) {
                    setState(() {
                      rotationX = value;
                      rotateModel();
                    });
                  },
                ),
                Text('Y Rotation: ${rotationY.toStringAsFixed(1)}'),
                Slider(
                  value: rotationY,
                  min: 0,
                  max: 360,
                  onChanged: (value) {
                    setState(() {
                      rotationY = value;
                      rotateModel();
                    });
                  },
                ),
                Text('Z Rotation: ${rotationZ.toStringAsFixed(1)}'),
                Slider(
                  value: rotationZ,
                  min: 0,
                  max: 360,
                  onChanged: (value) {
                    setState(() {
                      rotationZ = value;
                      rotateModel();
                    });
                  },
                ),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    rotationX = 0;
                    rotationY = 0;
                    rotationZ = 0;
                    rotateModel();
                  });
                },
                child: const Text('Reset'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    rotationX = 90;
                    rotationY = 0;
                    rotationZ = 0;
                    rotateModel();
                  });
                },
                child: const Text('Top View'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    rotationX = 0;
                    rotationY = 90;
                    rotationZ = 0;
                    rotateModel();
                  });
                },
                child: const Text('Side View'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
