import 'package:design_app/core/config/theme/app_theme.dart';
import 'package:design_app/presentation/home/<USER>/home_page.dart';
import 'package:flutter/material.dart';

class Root extends StatefulWidget {
  const Root({super.key});

  @override
  State<Root> createState() => _RootState();
}

class _RootState extends State<Root> {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      theme: AppTheme.lightTheme,
      debugShowCheckedModeBanner: false,
      home: const HomePage(),
      builder: (context, child) {
        if (child == null) return const SizedBox.shrink();
        return child;
      },
    );
  }
}
